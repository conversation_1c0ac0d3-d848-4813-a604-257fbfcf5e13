package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 每月运动步数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_monthly_steps")
public class MonthlySteps {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 年月格式：YYYY-MM
     */
    private String yearMonthStr;

    /**
     * 当月总步数
     */
    private Integer steps;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;
}
