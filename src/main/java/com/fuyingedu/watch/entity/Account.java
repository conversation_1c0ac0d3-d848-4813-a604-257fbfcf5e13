package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 运动手表用户账户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_account")
public class Account {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 性别 0-未知 1-男 2-女
     */
    private Byte gender;

    /**
     * 出生日期
     */
    private LocalDate birthDate;

    /**
     * 身高（单位：厘米）
     */
    private Integer height;

    /**
     * 体重（单位：克）
     */
    private Integer weight;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 运动宣言
     */
    private String slogan;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
