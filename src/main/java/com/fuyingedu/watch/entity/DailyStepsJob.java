package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * <p>
 * 每日运动步数表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_daily_steps_job")
public class DailyStepsJob {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 年月格式：YYYY-MM
     */
    private String yearMonthStr;

    /**
     * 本月统计的watch_daily_steps表的最大值
     */
    private Long maxId;
}
