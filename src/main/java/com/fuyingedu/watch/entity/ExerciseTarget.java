package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 运动目标记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_exercise_target")
public class ExerciseTarget {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 目标消耗的卡路里（单位：千卡）
     */
    private Integer caloriesBurned;

    /**
     * 锻炼时间（单位：分钟）
     */
    private Integer exerciseDuration;

    /**
     * 步数
     */
    private Integer stepCount;

    /**
     * 是否进行提醒：0-否，1-是
     */
    private Byte isReminderEnabled;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;
}
