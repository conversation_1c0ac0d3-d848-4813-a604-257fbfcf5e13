package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户身高体重变化记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_body_record")
public class BodyRecord {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账户ID，关联watch_account表
     */
    private Long accountId;

    /**
     * 身高（单位：厘米）
     */
    private Integer height;

    /**
     * 体重（单位：克）
     */
    private Integer weight;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
