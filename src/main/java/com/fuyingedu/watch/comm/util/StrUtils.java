package com.fuyingedu.watch.comm.util;

import java.util.List;

public class StrUtils {

    public static final List<String> endpoints = List.of("。", "？", "！", "；", "：", ",", ".", "?", "!", ":", ";");

    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static String underlineToCamel(String word) {
        if (word == null) {
            return null;
        }
        String[] split = word.split("_");
        StringBuilder sb = new StringBuilder(word.length());
        for (String s : split) {
            char[] chars = s.toCharArray();
            if(chars[0] >='a' && chars[0] <= 'z'){
                chars[0] -= 32;
            }
            sb.append(chars);
        }
        return sb.toString();
    }

    public static String camelToUnderline(String name) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < name.length(); ++i) {
            char ch = name.charAt(i);
            if (ch >= 'A' && ch <= 'Z') {
                char newChar = (char) (ch + 32);
                if (i > 0) {
                    buf.append('_');
                }
                buf.append(newChar);
            } else {
                buf.append(ch);
            }
        }
        return buf.toString();
    }

    /**
     * 去除文本中的markdown标记和表情符号
     *
     * @param text 原始文本
     * @return 清理后的文本
     */
    public static String cleanText(String text) {
        if (isEmpty(text)) {
            return text;
        }

        // 去除markdown标记
        // 去除标题标记
        String result = text.replaceAll("^#{1,6}\\s*", "");
        
        // 去除粗体和斜体标记
        result = result.replaceAll("\\*\\*", "");
        result = result.replaceAll("\\*", "");
        result = result.replaceAll("_{2}", "");
        result = result.replaceAll("_", "");
        
        // 去除代码块标记
        result = result.replaceAll("`{3}[\\s\\S]*?`{3}", "");
        result = result.replaceAll("`", "");
        
        // 去除链接和图片标记
        result = result.replaceAll("\\[([^\\]]*)]\\([^\\)]*\\)", "$1");
        
        // 去除引用标记
        result = result.replaceAll("^>\\s*", "");
        
        // 去除分割线
        result = result.replaceAll("^[-*]{3,}\\s*$", "");
        
        // 去除列表标记
        result = result.replaceAll("^[\\d]+\\.\\s*", "");
        result = result.replaceAll("^[\\-*+]\\s*", "");
        
        // 去除行内代码
        result = result.replaceAll("\\s`[^`]*`\\s", " ");
        
        // 去除表情符号 (基本表情符号范围)
        result = result.replaceAll("[\\uD83C-\\uDBFF\\uDC00-\\uDFFF]", "");
        
        // 去除其他常见的表情符号范围
        result = result.replaceAll("[\\u2600-\\u26FF]", "");
        result = result.replaceAll("[\\u2700-\\u27BF]", "");
        
        return result;
    }

}