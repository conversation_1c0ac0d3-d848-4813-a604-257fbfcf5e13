package com.fuyingedu.watch.comm.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.security.KeyStore;
import java.security.PublicKey;
import java.security.cert.Certificate;

@Slf4j
public class JwtUtils {


    private static final PublicKey publicKey; // 你的公钥

    // 初始化公钥
    static {
        // 从配置文件或密钥库加载公钥
         publicKey = loadPublicKey();
    }

    public static PublicKey loadPublicKey() {
        try {
            String alias = "abm-oauth-jwt";
            String keyStorePassword = "abm-pass";
            ClassPathResource ksFile = new ClassPathResource("abm-jwt.jks");
            KeyStore keyStore = KeyStore.getInstance("JKS");
            // 加载密钥库
            keyStore.load(ksFile.getInputStream(), keyStorePassword.toCharArray());
            // 获取证书
            Certificate certificate = keyStore.getCertificate(alias);
            // 返回公钥
            return certificate.getPublicKey();
        } catch (Exception e) {
            throw new JwtException("加载公钥失败", e);
        }
    }

    /**
     * 从Authorization头解析JWT Token并获取uid
     * @param authorizationHeader Authorization头的值，格式为 Bearer <token>"
     * @return 用户ID
     */
    public static Long getUidFromToken(String authorizationHeader) {
        if (authorizationHeader == null || !authorizationHeader.startsWith("Bearer ")) {
            return null;
        }
        try {
            // 提取Token
            String token = authorizationHeader.substring(7);
            // 去掉 "Bearer " 前缀

            // 解析JWT Token
            Jws<Claims> claimsJws = Jwts.parserBuilder()
                    .setSigningKey(publicKey) //
                    .build()
                    .parseClaimsJws(token);

            // 获取Claims中的uid
            Claims claims = claimsJws.getBody();
            Object uidObj = claims.get("uid"); //

            if (uidObj instanceof Long) {
                return (Long) uidObj;
            } else if (uidObj instanceof Integer) {
                return ((Integer) uidObj).longValue();
            } else if (uidObj instanceof String) {
                return Long.parseLong((String) uidObj);
            }
            return null;
        } catch (Throwable e) {
            log.error("解析Token错误", e);
            return null;
        }
    }

}
