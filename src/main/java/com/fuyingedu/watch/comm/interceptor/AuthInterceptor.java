package com.fuyingedu.watch.comm.interceptor;

import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.RespMeta;
import com.fuyingedu.watch.comm.util.JsonUtils;
import com.fuyingedu.watch.comm.util.JwtUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        if (HttpMethod.OPTIONS.name().equals(request.getMethod())) {
            return true;
        }
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }
        Long uid = JwtUtils.getUidFromToken(request.getHeader(HttpHeaders.AUTHORIZATION));
        if (!handlerMethod.hasMethodAnnotation(Login.class)) {
            if (uid != null) {
                request.setAttribute("userInfo", uid);
            }
            return true;
        }
        if (uid == null) {
            resp(response);
            return false;
        }
        request.setAttribute("userInfo", uid);
        return true;
    }

    protected void resp(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JsonUtils.formatObjToJson(CommResp.warning(RespMeta.NOT_LOGIN)));
    }
}
