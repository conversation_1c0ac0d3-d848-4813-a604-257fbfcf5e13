package com.fuyingedu.watch.comm.interceptor;

import com.fuyingedu.watch.comm.exception.WebBaseException;
import com.fuyingedu.watch.comm.util.StrUtils;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.RespMeta;
import io.jsonwebtoken.JwtException;
import io.micrometer.core.instrument.config.validate.ValidationException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public CommResp<?> missingServletRequestParameterException(
            HttpServletRequest request,
            MissingServletRequestParameterException e
    ) {
        printLog(request.getRequestURI(), e);
        return CommResp.warning(RespMeta.PARAM_ERROR);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommResp<?> methodArgumentNotValidException(
            HttpServletRequest request,
            MethodArgumentNotValidException e
    ) {
        printLog(request.getRequestURI(), e);
        String errMsg = "参数错误";
        FieldError fieldError = e.getBindingResult().getFieldError();
        if (fieldError != null && StrUtils.isNotEmpty(fieldError.getDefaultMessage())) {
            errMsg = fieldError.getDefaultMessage();
        }
        return CommResp.warning(RespMeta.PARAM_ERROR.getStatus(), errMsg);
    }

    @ExceptionHandler(ValidationException.class)
    public CommResp<?> validationException(
            HttpServletRequest request,
            ValidationException e
    ) {
        printLog(request.getRequestURI(), e);
        return CommResp.warning(RespMeta.PARAM_ERROR);
    }

    @ExceptionHandler(JwtException.class)
    public CommResp<?> tokenExpiredException(
            HttpServletRequest request,
            HttpServletResponse response,
            JwtException e
    ) {
        // 设置HTTP请求状态码
        printLog(request.getRequestURI(), e);
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        return CommResp.warning(RespMeta.NOT_LOGIN);
    }

    @ExceptionHandler(WebBaseException.class)
    public CommResp<?> webBaseException(HttpServletRequest request, WebBaseException e) {
        printLog(request.getRequestURI(), e);
        return CommResp.warning(e.getStatus(), e.getMsg());
    }

    @ExceptionHandler(Throwable.class)
    public CommResp<?> defaultErrorView(HttpServletRequest request, Throwable e) {
        printLog(request.getRequestURI(), e);
        return CommResp.warning(RespMeta.SERVER_ERROR);
    }

    private void printLog(String uri, Throwable e) {
        if (log.isWarnEnabled()) {
            if (e instanceof WebBaseException resp && e.getCause() == null) {
                log.warn("[{}][{}]", uri, resp.getMsg());
            } else {
                log.warn("======[{}]======", uri, e);
            }
        }
    }
}
