package com.fuyingedu.watch.comm.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fuyingedu.watch.comm.util.BeanUtils;
import com.fuyingedu.watch.comm.util.JsonUtils;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

@Configuration
public class UtilsConfig {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ResourceLoader resourceLoader;

    @PostConstruct
    public void init() {
        JsonUtils.init(objectMapper);
        BeanUtils.init(applicationContext);
    }
}
