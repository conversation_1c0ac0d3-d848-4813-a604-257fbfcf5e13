package com.fuyingedu.watch.comm.exception;

import com.fuyingedu.watch.model.RespMeta;
import lombok.Getter;

@Getter
public class WebBaseException extends RuntimeException {

    private final String status;
    private final String msg;

    public WebBaseException(String msg) {
        super(msg);
        this.status = RespMeta.SERVER_ERROR.getStatus();
        this.msg = msg;
    }

    public WebBaseException(String status, String msg) {
        super(msg);
        this.status = status;
        this.msg = msg;
    }


    public WebBaseException(String msg, Throwable throwable) {
        super(msg, throwable);
        this.status = RespMeta.SERVER_ERROR.getStatus();
        this.msg = msg;
    }

    public WebBaseException(String status, String msg, Throwable throwable) {
        super(msg, throwable);
        this.status = status;
        this.msg = msg;
    }

    public WebBaseException(RespMeta code) {
        this(code.getStatus(), code.getMsg());
    }

    public WebBaseException(RespMeta code, Throwable throwable) {
        this(code.getStatus(), code.getMsg(), throwable);
    }

}
