package com.fuyingedu.watch.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fuyingedu.watch.entity.*;
import com.fuyingedu.watch.mapper.*;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.IdReq;
import com.fuyingedu.watch.model.steps.ReportResp;
import com.fuyingedu.watch.model.steps.SaveReq;
import com.fuyingedu.watch.comm.util.DateUtils;
import com.fuyingedu.watch.model.steps.StatResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class StepsService {

    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private DailyStepsMapper dailyStepsMapper;
    @Autowired
    private MonthlyStepsMapper monthlyStepsMapper;
    @Autowired
    private YearlyStepsMapper yearlyStepsMapper;
    @Autowired
    private DailyStepsJobMapper dailyStepsJobMapper;

    /**
     * 保存或更新今日步数
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> saveOrUpdateDailySteps(Long uid, SaveReq req) {
        // 验证账号是否属于当前用户
        LambdaQueryWrapper<Account> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.select(Account::getId)
                .eq(Account::getUid, uid)
                .eq(Account::getId, req.getAccountId());
        Account account = accountMapper.selectOne(accountQueryWrapper);

        if (account == null) {
            return CommResp.error("用户账号不存在或无权限操作");
        }

        // 查询今日步数记录
        LambdaQueryWrapper<DailySteps> dailyStepsQueryWrapper = new LambdaQueryWrapper<>();
        dailyStepsQueryWrapper.eq(DailySteps::getAccountId, req.getAccountId())
                .eq(DailySteps::getStepDate, LocalDate.now());
        DailySteps dailySteps = dailyStepsMapper.selectOne(dailyStepsQueryWrapper);

        if (dailySteps != null) {
            // 更新今日步数
            dailySteps.setSteps(req.getSteps());
            dailyStepsMapper.updateById(dailySteps);
        } else {
            // 创建今日步数记录
            dailySteps = new DailySteps();
            dailySteps.setAccountId(req.getAccountId());
            dailySteps.setStepDate(LocalDate.now());
            dailySteps.setSteps(req.getSteps());
            dailyStepsMapper.insert(dailySteps);
        }

        return CommResp.success();
    }

    /**
     * 按日、周、月、年统计步数
     * 1. 日：直接从 daily_steps 表中查询最近30天的数据，如果某一天没有就补0
     * 2. 周：查询本周和上周的数据，从日表中查询并统计
     * 3. 月：从 monthly_steps 表中查询最近6个月的数据
     * 4. 年：从 yearly_steps 表中查询最近5年的数据
     */
    public CommResp<List<StatResp>> statSteps(Long uid, Long accountId, Integer type) {
        // 验证账号是否属于当前用户
        LambdaQueryWrapper<Account> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.select(Account::getId);
        accountQueryWrapper.eq(Account::getUid, uid).eq(Account::getId, accountId);
        Account account = accountMapper.selectOne(accountQueryWrapper);
        
        if (account == null) {
            return CommResp.error("用户账号不存在");
        }
        
        return switch (type) {
            case 1 -> // 按日统计
                    statDailySteps(accountId);
            case 2 -> // 按周统计
                    statWeeklySteps(accountId);
            case 3 -> // 按月统计
                    statMonthlySteps(accountId);
            case 4 -> // 按年统计
                    statYearlySteps(accountId);
            default -> CommResp.error("不支持的统计类型");
        };
    }
    
    /**
     * 按日统计最近30天的步数
     */
    private CommResp<List<StatResp>> statDailySteps(Long accountId) {
        // 计算最近30天的日期范围
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(29); // 30天包括今天
        
        // 查询日期范围内的步数数据
        LambdaQueryWrapper<DailySteps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DailySteps::getAccountId, accountId);
        queryWrapper.ge(DailySteps::getStepDate, startDate);
        queryWrapper.le(DailySteps::getStepDate, endDate);
        List<DailySteps> dailyStepsList = dailyStepsMapper.selectList(queryWrapper);
        
        // 按日期分组步数数据
        Map<LocalDate, DailySteps> dateStepsMap = new HashMap<>();
        for (DailySteps dailySteps : dailyStepsList) {
            LocalDate date = dailySteps.getStepDate();
            dateStepsMap.put(date, dailySteps);
        }
        
        // 构建最近30天的日期列表，缺失日期补0
        List<StatResp> result = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            LocalDate date = startDate.plusDays(i);
            StatResp item = new StatResp();
            if (dateStepsMap.containsKey(date)) {
                DailySteps dailySteps = dateStepsMap.get(date);
                item.setId(dailySteps.getId());
                item.setSteps(dailySteps.getSteps() != null ? dailySteps.getSteps() : 0);
            } else {
                item.setId(0L);
                item.setSteps(0);
            }
            item.setDesc(date.toString());
            result.add(item);
        }
        
        return CommResp.success(result);
    }
    
    /**
     * 按周统计本周和上周的步数
     */
    private CommResp<List<StatResp>> statWeeklySteps(Long accountId) {
        LocalDate today = LocalDate.now();
        
        // 计算本周的开始和结束日期
        LocalDate thisWeekStart = today.minusDays(today.getDayOfWeek().getValue() - 1);
        LocalDate thisWeekEnd = thisWeekStart.plusDays(6);
        
        // 计算上周的开始和结束日期
        LocalDate lastWeekStart = thisWeekStart.minusDays(7);
        LocalDate lastWeekEnd = thisWeekStart.minusDays(1);
        
        // 查询本周步数数据
        LambdaQueryWrapper<DailySteps> thisWeekQuery = new LambdaQueryWrapper<>();
        thisWeekQuery.eq(DailySteps::getAccountId, accountId);
        thisWeekQuery.ge(DailySteps::getStepDate, thisWeekStart);
        thisWeekQuery.le(DailySteps::getStepDate, thisWeekEnd);
        List<DailySteps> thisWeekSteps = dailyStepsMapper.selectList(thisWeekQuery);
        
        // 查询上周步数数据
        LambdaQueryWrapper<DailySteps> lastWeekQuery = new LambdaQueryWrapper<>();
        lastWeekQuery.eq(DailySteps::getAccountId, accountId);
        lastWeekQuery.ge(DailySteps::getStepDate, lastWeekStart);
        lastWeekQuery.le(DailySteps::getStepDate, lastWeekEnd);
        List<DailySteps> lastWeekSteps = dailyStepsMapper.selectList(lastWeekQuery);
        
        // 计算本周总步数
        int thisWeekTotal = 0;
        for (DailySteps dailySteps : thisWeekSteps) {
            thisWeekTotal += dailySteps.getSteps() != null ? dailySteps.getSteps() : 0;
        }
        
        // 计算上周总步数
        int lastWeekTotal = 0;
        for (DailySteps dailySteps : lastWeekSteps) {
            lastWeekTotal += dailySteps.getSteps() != null ? dailySteps.getSteps() : 0;
        }
        
        List<StatResp> result = new ArrayList<>();
        StatResp thisWeek = new StatResp();
        thisWeek.setId(1L);
        thisWeek.setDesc("本周");
        thisWeek.setSteps(thisWeekTotal);
        result.add(thisWeek);
        
        StatResp lastWeek = new StatResp();
        lastWeek.setId(2L);
        lastWeek.setDesc("上周");
        lastWeek.setSteps(lastWeekTotal);
        result.add(lastWeek);
        
        return CommResp.success(result);
    }
    
    /**
     * 按月统计最近6个月的步数
     */
    private CommResp<List<StatResp>> statMonthlySteps(Long accountId) {
        // 计算最近6个月的年月列表
        LocalDate now = LocalDate.now();
        List<String> recentMonths = new ArrayList<>();
        for (int i = 5; i >= 0; i--) {
            LocalDate month = now.minusMonths(i);
            recentMonths.add(DateUtils.format(month, DateUtils.YEAR_MONTH_FORMATTER));
        }
        
        // 查询最近6个月的步数数据
        LambdaQueryWrapper<MonthlySteps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MonthlySteps::getAccountId, accountId);
        queryWrapper.in(MonthlySteps::getYearMonthStr, recentMonths);
        List<MonthlySteps> monthlyStepsList = monthlyStepsMapper.selectList(queryWrapper);
        
        // 按年月分组步数数据
        Map<String, MonthlySteps> monthStepsMap = new HashMap<>();
        for (MonthlySteps monthlySteps : monthlyStepsList) {
            String yearMonth = monthlySteps.getYearMonthStr();
            monthStepsMap.put(yearMonth, monthlySteps);
        }
        
        // 构建结果，缺失月份补0
        List<StatResp> result = new ArrayList<>();
        for (String yearMonth : recentMonths) {
            StatResp item = new StatResp();
            if (monthStepsMap.containsKey(yearMonth)) {
                MonthlySteps monthlySteps = monthStepsMap.get(yearMonth);
                item.setId(monthlySteps.getId());
                item.setSteps(monthlySteps.getSteps() != null ? monthlySteps.getSteps() : 0);
            } else {
                item.setId(0L);
                item.setSteps(0);
            }
            item.setDesc(yearMonth);
            result.add(item);
        }
        
        return CommResp.success(result);
    }
    
    /**
     * 按年统计最近5年的步数
     */
    private CommResp<List<StatResp>> statYearlySteps(Long accountId) {
        // 计算最近5年的年份列表
        int currentYear = LocalDate.now().getYear();
        List<String> recentYears = new ArrayList<>();
        for (int i = 4; i >= 0; i--) {
            recentYears.add(String.valueOf(currentYear - i));
        }
        
        // 查询最近5年的步数数据
        LambdaQueryWrapper<YearlySteps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YearlySteps::getAccountId, accountId);
        queryWrapper.in(YearlySteps::getYearStr, recentYears);
        List<YearlySteps> yearlyStepsList = yearlyStepsMapper.selectList(queryWrapper);
        
        // 按年份分组步数数据
        Map<String, YearlySteps> yearStepsMap = new HashMap<>();
        for (YearlySteps yearlySteps : yearlyStepsList) {
            String year = yearlySteps.getYearStr();
            yearStepsMap.put(year, yearlySteps);
        }
        
        // 构建结果，缺失年份补0
        List<StatResp> result = new ArrayList<>();
        for (String year : recentYears) {
            StatResp item = new StatResp();
            if (yearStepsMap.containsKey(year)) {
                YearlySteps yearlySteps = yearStepsMap.get(year);
                item.setId(yearlySteps.getId());
                item.setSteps(yearlySteps.getSteps() != null ? yearlySteps.getSteps() : 0);
            } else {
                item.setId(0L);
                item.setSteps(0);
            }
            item.setDesc(year);
            result.add(item);
        }
        
        return CommResp.success(result);
    }

    /**
     * 步数报表查询
     * 1. 按日查询：先不做TODO
     * 2. 按周查询：根据objId判断是本周还是上周的数据，没有的补0
     * 3. 按月查询：根据objId从月表中查询出月份，然后从日表中查询出数据，没有的补0
     * 4. 按年查询：根据objId从年报表中查询出年份，然后从月表中查询出数据，没有的补0
     */
    public CommResp<List<ReportResp>> reportSteps(Long uid, Long accountId, Long objId, Integer type) {
        return null;
    }

    /**
     * 按月和年统计步数并写入到 monthly_steps 和 yearly_steps 表中
     * 1. 从日表中查询本月的数据并计算后写入到月表中。
     * 2. 从月表中查询本年数据并计算后写入到年表中。
     */
    public void calculateMonthlyAndYearlySteps() {
        LocalDate now = LocalDate.now().minusDays(1);
        String lastMonth = DateUtils.format(now.minusMonths(1), DateUtils.YEAR_MONTH_FORMATTER);
        DailyStepsJob dailyStepsJob = dailyStepsJobMapper.selectOne(new LambdaQueryWrapper<DailyStepsJob>()
                .eq(DailyStepsJob::getYearMonthStr, lastMonth));
        Long minId = 0L;
        if (dailyStepsJob != null) {
            minId = dailyStepsJob.getMaxId();
        }

        // 1. 从日表中查询本月的数据并计算后写入到月表中
        // 获取本月第一天和最后一天
        LocalDate firstDayOfMonth = now.withDayOfMonth(1);
        LocalDate lastDayOfMonth = now.withDayOfMonth(now.lengthOfMonth());
        
        // 查询本月所有用户的步数数据
        LambdaQueryWrapper<DailySteps> dailyQueryWrapper = new LambdaQueryWrapper<>();
        dailyQueryWrapper.ge(DailySteps::getStepDate, firstDayOfMonth)
                .le(DailySteps::getStepDate, lastDayOfMonth)
                .gt(DailySteps::getId, minId).orderByAsc(DailySteps::getId);
        List<DailySteps> dailyStepsList = dailyStepsMapper.selectList(dailyQueryWrapper);
        
        // 按用户ID分组计算每月步数
        Map<Long, Integer> monthlyStepsMap = new HashMap<>();
        for (DailySteps dailyStep : dailyStepsList) {
            Long accountId = dailyStep.getAccountId();
            Integer steps = dailyStep.getSteps() != null ? dailyStep.getSteps() : 0;
            monthlyStepsMap.put(accountId, monthlyStepsMap.getOrDefault(accountId, 0) + steps);
        }
        
        // 保存或更新每月步数数据
        String yearMonthStr = DateUtils.format(now, DateUtils.YEAR_MONTH_FORMATTER);
        for (Map.Entry<Long, Integer> entry : monthlyStepsMap.entrySet()) {
            Long accountId = entry.getKey();
            Integer totalSteps = entry.getValue();
            
            // 查询是否已存在该月的记录
            LambdaQueryWrapper<MonthlySteps> monthlyQueryWrapper = new LambdaQueryWrapper<>();
            monthlyQueryWrapper.eq(MonthlySteps::getAccountId, accountId)
                    .eq(MonthlySteps::getYearMonthStr, yearMonthStr);
            MonthlySteps monthlySteps = monthlyStepsMapper.selectOne(monthlyQueryWrapper);
            
            if (monthlySteps != null) {
                // 更新记录
                monthlySteps.setSteps(totalSteps);
                monthlyStepsMapper.updateById(monthlySteps);
            } else {
                // 创建新记录
                monthlySteps = new MonthlySteps();
                monthlySteps.setAccountId(accountId);
                monthlySteps.setYearMonthStr(yearMonthStr);
                monthlySteps.setSteps(totalSteps);
                monthlyStepsMapper.insert(monthlySteps);
            }
        }
        
        // 2. 从月表中查询本年数据并计算后写入到年表中
        // 获取本年第一天和最后一天的月份
        String firstMonthOfYear = now.getYear() + "-01";
        String lastMonthOfYear = now.getYear() + "-12";
        
        // 查询本年所有用户的月步数数据
        LambdaQueryWrapper<MonthlySteps> monthlyQueryWrapper = new LambdaQueryWrapper<>();
        monthlyQueryWrapper.ge(MonthlySteps::getYearMonthStr, firstMonthOfYear)
                .le(MonthlySteps::getYearMonthStr, lastMonthOfYear);
        List<MonthlySteps> monthlyStepsList = monthlyStepsMapper.selectList(monthlyQueryWrapper);
        
        // 按用户ID分组计算每年步数
        Map<Long, Integer> yearlyStepsMap = new HashMap<>();
        for (MonthlySteps monthlyStep : monthlyStepsList) {
            Long accountId = monthlyStep.getAccountId();
            Integer steps = monthlyStep.getSteps() != null ? monthlyStep.getSteps() : 0;
            yearlyStepsMap.put(accountId, yearlyStepsMap.getOrDefault(accountId, 0) + steps);
        }
        
        // 保存或更新每年步数数据
        String yearStr = String.valueOf(now.getYear());
        for (Map.Entry<Long, Integer> entry : yearlyStepsMap.entrySet()) {
            Long accountId = entry.getKey();
            Integer totalSteps = entry.getValue();
            
            // 查询是否已存在该年的记录
            LambdaQueryWrapper<YearlySteps> yearlyQueryWrapper = new LambdaQueryWrapper<>();
            yearlyQueryWrapper.eq(YearlySteps::getAccountId, accountId)
                    .eq(YearlySteps::getYearStr, yearStr);
            YearlySteps yearlySteps = yearlyStepsMapper.selectOne(yearlyQueryWrapper);
            
            if (yearlySteps != null) {
                // 更新记录
                yearlySteps.setSteps(totalSteps);
                yearlySteps.setUpdatedAt(LocalDateTime.now());
                yearlyStepsMapper.updateById(yearlySteps);
            } else {
                // 创建新记录
                yearlySteps = new YearlySteps();
                yearlySteps.setAccountId(accountId);
                yearlySteps.setYearStr(yearStr);
                yearlySteps.setSteps(totalSteps);
                yearlySteps.setCreatedAt(LocalDateTime.now());
                yearlySteps.setUpdatedAt(LocalDateTime.now());
                yearlyStepsMapper.insert(yearlySteps);
            }
        }

        // 更新 watch_daily_steps_job 表
        DailyStepsJob job = dailyStepsJobMapper.selectOne(new LambdaQueryWrapper<DailyStepsJob>()
                .eq(DailyStepsJob::getYearMonthStr, yearMonthStr));
        if (job == null) {
            job = new DailyStepsJob();
            job.setYearMonthStr(yearMonthStr);
        }
        job.setMaxId(dailyStepsList.getLast().getId());
        dailyStepsJobMapper.insertOrUpdate(job);
    }

}