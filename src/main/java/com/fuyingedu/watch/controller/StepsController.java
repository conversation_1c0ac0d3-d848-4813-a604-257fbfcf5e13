package com.fuyingedu.watch.controller;

import com.fuyingedu.watch.comm.interceptor.Login;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.IdReq;
import com.fuyingedu.watch.model.steps.ReportResp;
import com.fuyingedu.watch.model.steps.SaveReq;
import com.fuyingedu.watch.model.steps.StatResp;
import com.fuyingedu.watch.service.StepsService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 步数管理
 */
@RestController
@RequestMapping("/api/watch/steps")
public class StepsController {

    @Autowired
    private StepsService stepsService;

    /**
     * 保存或更新今日步数
     */
    @Login
    @PostMapping("save")
    public CommResp<?> saveOrUpdateDailySteps(
            @Login Long uid,
            @RequestBody @Valid SaveReq req
    ) {
        return stepsService.saveOrUpdateDailySteps(uid, req);
    }

    /**
     * 按日、周、月、年统计步数
     * @param type 1-日 2-周 3-月 4-年
     */
    @Login
    @GetMapping("stat")
    public CommResp<List<StatResp>> statSteps(
            @Login Long uid,
            @RequestParam Long accountId,
            @RequestParam Integer type
    ) {
        return stepsService.statSteps(uid, accountId, type);
    }

    /**
     * 步数报表查询
     * @param objId 统计列表返回的ID
     * @param type 1-日 2-周 3-月 4-年
     */
    @Login
    @GetMapping("report")
    public CommResp<List<ReportResp>> reportSteps(
            @Login Long uid,
            @RequestParam Long accountId,
            @RequestParam Long objId,
            @RequestParam Integer type
    ) {
        return stepsService.reportSteps(uid, accountId, objId, type);
    }
}