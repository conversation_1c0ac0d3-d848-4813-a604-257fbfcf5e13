package com.fuyingedu.watch.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PageReq {

    /**
     * 当前页码
     */
    private long current;
    /**
     * 每页数量
     */
    private long size;

    public long getCurrent() {
        if (current < 1) {
            return 1;
        }
        return current;
    }

    public long getPageSize() {
        if (size < 1) {
            return 15;
        }
        return size;
    }

    public long getOffset() {
        return (getCurrent() - 1) * getPageSize();
    }
}
