package com.fuyingedu.watch.model.account;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SaveExerciseTargetReq {

    /**
     * 账户ID
     */
    @NotNull
    private Long accountId;

    /**
     * 目标消耗的卡路里（单位：千卡）
     */
    private Integer caloriesBurned;

    /**
     * 锻炼时间（单位：分钟）
     */
    private Integer exerciseDuration;

    /**
     * 步数
     */
    private Integer stepCount;

    /**
     * 是否进行提醒：0-否，1-是
     */
    private Byte isReminderEnabled;
}
