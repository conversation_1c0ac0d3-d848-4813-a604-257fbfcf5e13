package com.fuyingedu.watch.model.account;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
public class InitReq {
    
    /**
     * 性别 1-男 2-女
     */
    @NotNull
    private Byte gender;
    
    /**
     * 出生日期
     */
    @NotNull
    private LocalDate birthDate;
    
    /**
     * 身高（单位：厘米）
     */
    @NotNull
    private Integer height;
    
    /**
     * 体重（单位：克）
     */
    @NotNull
    private Integer weight;
    
}