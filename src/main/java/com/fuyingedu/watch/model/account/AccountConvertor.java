package com.fuyingedu.watch.model.account;

import com.fuyingedu.watch.entity.Account;
import com.fuyingedu.watch.entity.ExerciseTarget;

public class AccountConvertor {
    public static Account toAccount(InitReq req) {
        Account account = new Account();
        account.setGender(req.getGender() != null ? req.getGender() : (byte) 0);
        account.setBirthDate(req.getBirthDate());
        account.setHeight(req.getHeight());
        account.setWeight(req.getWeight());
        return account;
    }

    public static ExerciseTarget toExerciseTarget(SaveExerciseTargetReq req) {
        ExerciseTarget updateTarget = new ExerciseTarget();
        updateTarget.setId(req.getAccountId());
        updateTarget.setCaloriesBurned(req.getCaloriesBurned());
        updateTarget.setExerciseDuration(req.getExerciseDuration());
        updateTarget.setStepCount(req.getStepCount());
        updateTarget.setIsReminderEnabled(req.getIsReminderEnabled());
        return updateTarget;
    }
}
