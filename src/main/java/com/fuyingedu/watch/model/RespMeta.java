package com.fuyingedu.watch.model;

import lombok.Getter;

@Getter
public enum RespMeta {

    /**
     * 成功
     */
    SUCCESS("200", "成功"),

    /**
     * 默认异常状态
     */
    SERVER_ERROR("500", "系统繁忙，请稍后重试！"),

    NOT_LOGIN("488004", "未登陆或登陆过期"),


    PARAM_ERROR("101", "参数错误"),

    ;

    private final String status;
    private final String msg;

    RespMeta(String status, String msg) {
        this.status = status;
        this.msg = msg;
    }

}
