spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************************************
    username: db_user_prod
    password: X1P0dYPXw!m0uUkQ@TV2
    hikari:
      pool-name: hikari
      minimum-idle: 10
      maximum-pool-size: 20
      connection-test-query: SELECT 1
  data:
    redis:
      host: r-bp112tkj0hi5obein2.redis.rds.aliyuncs.com
      port: 6379
      timeout: 30000
      database: 19
