import com.fuyingedu.watch.Application;
import com.fuyingedu.watch.comm.util.JwtUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = Application.class)
public class JwtTest {

    @Test
    public void test() {
        // "Bearer eyJraWQiOiI4YmRlOGViYS00YWIzLTQwNzYtOTVlMS1iOTEyYWZlYjVjZGYiLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************.LNY9xVk9b3V_AGecglOFAUhYJafWvM7IN85zA2tt0fd8AfRQm2w-L78EEJyfPzXNdMy4FGcbWrcsRNhrnoT9Tj1T91gZ5MCzYJQhUojFRhuFMWxftknNi3mD6fu9ytMpT_1yeo3fuuvMW0SgQUrtDF6U2DOIP8mttiJhrGMFSr_t4YMsFhQ7OnnihRophFQY1J9P_1wjLclm8T6m17Akv8Zx4GTZgHGeC1VbjljazF3qpj6O7lcNXH12SbDEdLfITxVaFDME9OkDswHAQPHOaHr_sZWMvAllf5Njj6TC_3tT-NrTvlwngOFoDWIQY85ko-6QCOQB6svtFCkFyIwCKg"
        Long uidFromToken = JwtUtils.getUidFromToken("Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        System.out.println(uidFromToken);
    }
}
