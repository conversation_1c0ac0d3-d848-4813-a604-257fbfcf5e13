apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fuying-watch
  name: fuying-watch
spec:
  selector:
    matchLabels:
      app: fuying-watch
  template:
    metadata:
      labels:
        app: fuying-watch
    spec:
      containers:
        - env:
            - name: TZ
              value: Asia/Shanghai
          image: ${IMAGE}
          imagePullPolicy: Always
          name: fuying-watch
          ports:
            - containerPort: 8088
              protocol: TCP