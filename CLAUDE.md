# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Spring Boot 3.x application written in Java 21 that provides a web API with WebSocket support. The application uses JWT-based authentication and integrates with Redis for data storage. It follows a typical Spring Boot architecture with configuration, interceptor, utility, and exception handling components.

## Key Technologies

- Java 21
- Spring Boot 3.4.4
- <PERSON><PERSON> for build management
- MyBatis Plus for database operations
- Redis for caching
- JW<PERSON> for authentication
- WebSocket for real-time communication
- Lombok for reducing boilerplate code
- JUnit 5 for testing

## Project Structure

```
src/
├── main/
│   └── java/
│       └── com/fuyingedu/watch/
│           ├── Application.java (main entry point)
│           ├── CodeGenerator.java (MyBatis Plus code generator)
│           ├── comm/
│           │   ├── config/ (Spring configuration classes)
│           │   ├── exception/ (Custom exception handling)
│           │   ├── interceptor/ (Authentication and request interceptors)
│           │   └── util/ (Utility classes)
│           └── model/ (Data models)
└── test/
    └── java/ (Test classes)
```

## Common Development Tasks

### Building the Project
```bash
mvn clean package
```

### Running Tests
```bash
mvn test
```

### Running the Application
```bash
mvn spring-boot:run
```

### Building Docker Image
1. First build the JAR: `mvn clean package`
2. Then build the Docker image: `docker build -t fuying-watch .`

## Key Components

1. **Authentication**: Uses JWT tokens with RSA public key verification. The public key is loaded from a JKS keystore file.
2. **Interceptors**: AuthInterceptor handles authentication for API endpoints.
3. **Configuration**: Various Spring configuration classes for Web, Redis, MyBatis Plus, etc.
4. **Utilities**: Helper classes for JSON processing, date handling, JWT parsing, etc.

## Deployment

The application is deployed using Kubernetes with a deployment manifest in the `manifests/` directory. The Dockerfile is configured to run the application on port 8088.

## Development Notes

- All Spring components are scanned under the `com.fuyingedu.watch` package
- Authentication is required for endpoints under `/api/**` unless they are excluded
- The `@Login` annotation can be used on controller methods to require authentication
- JWT tokens are expected in the Authorization header with "Bearer " prefix